2025-08-02 14:14:26 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-02 14:14:26 - asyncio - DEBUG - [proactor_events.py:629] - Using proactor: IocpProactor
2025-08-02 15:04:10 - root - INFO - [logger.py:89] - 日志器 'None' 初始化完成，级别: DEBUG
2025-08-02 15:04:10 - asyncio - DEBUG - [proactor_events.py:629] - Using proactor: IocpProactor
2025-08-02 15:05:53 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:05:53 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:05:53 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:05:53 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:05:53 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:05:53 - root - INFO - [client.py:117] - [2841923940560] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:05:53 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:05:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:05:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF42F90>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:05:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76720> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:05:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295ADD0C510>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:05:55 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820617541183548096275eb93789b4c028374b6d6f8478b50982e8c6be;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820617541183548096275eb93789b4c028374b6d6f8478b50982e8c6be;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd4466aa8-9f28-4d10-b6ad-b17b15c67667'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:55 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782
2025-08-02 15:05:55 - root - DEBUG - [client.py:129] - [2841923940560] SSE流连接已建立
2025-08-02 15:05:55 - root - DEBUG - [client.py:134] - [2841923940560] 客户端会话已创建
2025-08-02 15:05:55 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:05:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:05:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF66090>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:05:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76720> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:05:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFED8DD0>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:56 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fe9169eb-3bc4-48a2-8f85-8b29140da9c5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:56 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:05:56 - root - INFO - [client.py:138] - [2841923940560] SSE连接建立成功
2025-08-02 15:05:56 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:05:56 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f16ad518-e804-4316-86ec-4842408ae66e'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:05:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b24fac6d-7085-40b2-83d6-7fdf2e72110c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:05:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c4daca0de30c4a598e0791c7abcca782 "HTTP/1.1 202 Accepted"
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:05:57 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:05:57 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:05:57 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:05:57 - root - DEBUG - [client.py:214] - [2841923940560] 开始清理SSE连接资源...
2025-08-02 15:05:57 - root - DEBUG - [client.py:219] - [2841923940560] 会话上下文已清理
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afe58290')
2025-08-02 15:05:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:05:57 - root - DEBUG - [client.py:228] - [2841923940560] 上下文已清理
2025-08-02 15:05:57 - root - INFO - [client.py:234] - [2841923940560] SSE连接资源清理完成
2025-08-02 15:12:00 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:12:00 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:12:00 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:12:00 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:12:00 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:12:00 - root - INFO - [client.py:117] - [2841924542672] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:12:00 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:12:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:12:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE582D0>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:12:00 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76F90> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:12:01 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF17650>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:12:01 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820217541187212256810e35e7f796ac74365b8a528501fa3f72e95f18;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820217541187212256810e35e7f796ac74365b8a528501fa3f72e95f18;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7f885545-c5f1-4f3f-ba34-25ad3f6fa7eb'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:01 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1
2025-08-02 15:12:01 - root - DEBUG - [client.py:129] - [2841924542672] SSE流连接已建立
2025-08-02 15:12:01 - root - DEBUG - [client.py:134] - [2841924542672] 客户端会话已创建
2025-08-02 15:12:01 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:12:01 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:12:01 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEC8390>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:12:01 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:12:01 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76F90> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:12:02 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF43250>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:12:02 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fff54ca8-b4a8-4912-b1bc-ac493e3f2dc0'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:02 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1 "HTTP/1.1 202 Accepted"
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:12:02 - root - INFO - [client.py:138] - [2841924542672] SSE连接建立成功
2025-08-02 15:12:02 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:12:02 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'aae5b8d9-5dd5-45c1-8cea-fe4f5dbab3f5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:12:02 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=c69b76cc661a4d2e9eadf49fccc946f1 "HTTP/1.1 202 Accepted"
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:12:02 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:12:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:12:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:12:03 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:12:03 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:12:03 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:12:03 - root - DEBUG - [client.py:214] - [2841924542672] 开始清理SSE连接资源...
2025-08-02 15:12:03 - root - DEBUG - [client.py:219] - [2841924542672] 会话上下文已清理
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.failed exception=CancelledError('Cancelled by cancel scope 295afe4b990')
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afe4b990')
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:03 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:12:03 - root - DEBUG - [client.py:228] - [2841924542672] 上下文已清理
2025-08-02 15:12:03 - root - INFO - [client.py:234] - [2841924542672] SSE连接资源清理完成
2025-08-02 15:17:18 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:17:18 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:17:18 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:17:18 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:17:18 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:17:18 - root - INFO - [client.py:117] - [2841924964304] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:17:18 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:17:18 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:17:18 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08690>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:18 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:17:18 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77770> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:17:19 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08F50>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:17:20 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541190400126414e005f7507e4bdd0900c99dff9b597b8fe2a88;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541190400126414e005f7507e4bdd0900c99dff9b597b8fe2a88;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'e4bc33d3-de79-4487-b17e-e56e67574146'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:20 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb
2025-08-02 15:17:20 - root - DEBUG - [client.py:129] - [2841924964304] SSE流连接已建立
2025-08-02 15:17:20 - root - DEBUG - [client.py:134] - [2841924964304] 客户端会话已创建
2025-08-02 15:17:20 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:17:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:17:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF0BD90>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:17:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:17:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77770> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:17:21 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08E90>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'ae503f0d-d7ce-4762-9048-126c38d32a86'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:17:22 - root - INFO - [client.py:138] - [2841924964304] SSE连接建立成功
2025-08-02 15:17:22 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:22 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'85856136-bc48-4289-9fa4-7ee993fc7b27'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:22 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:22 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:17:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:17:23 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'62b137ca-7f98-4abe-abfa-1ecfb7801da1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:17:23 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=7c1ad1d27a0747eb824a3668e5f6fdbb "HTTP/1.1 202 Accepted"
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:17:23 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:17:23 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:17:23 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:17:23 - root - DEBUG - [client.py:214] - [2841924964304] 开始清理SSE连接资源...
2025-08-02 15:17:23 - root - DEBUG - [client.py:219] - [2841924964304] 会话上下文已清理
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff11d10')
2025-08-02 15:17:23 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:17:23 - root - DEBUG - [client.py:228] - [2841924964304] 上下文已清理
2025-08-02 15:17:23 - root - INFO - [client.py:234] - [2841924964304] SSE连接资源清理完成
2025-08-02 15:22:00 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:22:00 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:22:00 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:22:00 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:22:00 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:22:00 - root - INFO - [client.py:117] - [2841924967248] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:22:00 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:22:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:22:00 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF66990>
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:22:00 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:22:00 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77DA0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:22:02 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF66FD0>
2025-08-02 15:22:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:22:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:22:02 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:02 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:22:03 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820217541193228391222e35e2a22b322408d8fc31681c6d13d5ed7f20;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820217541193228391222e35e2a22b322408d8fc31681c6d13d5ed7f20;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4046573e-8dbb-4e67-92ef-94f3a85e4a01'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:22:03 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:22:03 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:22:03 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:22:03 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=73b86d515520414ea71a06fb039e434d
2025-08-02 15:22:03 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=73b86d515520414ea71a06fb039e434d
2025-08-02 15:22:03 - root - DEBUG - [client.py:129] - [2841924967248] SSE流连接已建立
2025-08-02 15:22:03 - root - DEBUG - [client.py:134] - [2841924967248] 客户端会话已创建
2025-08-02 15:22:03 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:22:03 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:22:03 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF152D0>
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:22:03 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:22:03 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77DA0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:22:04 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF12B10>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:22:04 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'3929144d-f2f3-43e0-9cf6-cfc6eec55d0e'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:22:04 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=73b86d515520414ea71a06fb039e434d "HTTP/1.1 202 Accepted"
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:22:04 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:22:04 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:22:04 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:22:04 - root - INFO - [client.py:138] - [2841924967248] SSE连接建立成功
2025-08-02 15:22:04 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:22:04 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:04 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:22:05 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7b1503e8-dc1f-4650-a588-0eb9670547b2'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:22:05 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=73b86d515520414ea71a06fb039e434d "HTTP/1.1 202 Accepted"
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:22:05 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:22:05 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:22:06 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'cbf619de-3cdf-46fd-a99a-cc5a3795aa7b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:22:05 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=73b86d515520414ea71a06fb039e434d "HTTP/1.1 202 Accepted"
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:22:05 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:22:05 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:22:06 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:22:06 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:22:06 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:22:06 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:22:06 - root - DEBUG - [client.py:214] - [2841924967248] 开始清理SSE连接资源...
2025-08-02 15:22:06 - root - DEBUG - [client.py:219] - [2841924967248] 会话上下文已清理
2025-08-02 15:22:06 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:22:06 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afedd8d0')
2025-08-02 15:22:06 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:22:06 - root - DEBUG - [client.py:228] - [2841924967248] 上下文已清理
2025-08-02 15:22:06 - root - INFO - [client.py:234] - [2841924967248] SSE连接资源清理完成
2025-08-02 15:29:40 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:29:40 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:29:40 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:29:40 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:29:40 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:29:40 - root - INFO - [client.py:117] - [2841925213904] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:29:40 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:29:40 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:29:40 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE5BF50>
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:29:40 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:29:40 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEC5F0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:29:41 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE70210>
2025-08-02 15:29:41 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:29:41 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:41 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:29:41 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:41 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:29:42 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820317541197820833015ed059200aa7293d686b59756456c388e39062;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820317541197820833015ed059200aa7293d686b59756456c388e39062;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'50408c9d-0ce7-4c14-945c-86eff5af76b5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:29:42 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:29:42 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:29:42 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:29:42 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=b0b9daaa5e2a478690c176ab2bee0d24
2025-08-02 15:29:42 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=b0b9daaa5e2a478690c176ab2bee0d24
2025-08-02 15:29:42 - root - DEBUG - [client.py:129] - [2841925213904] SSE流连接已建立
2025-08-02 15:29:42 - root - DEBUG - [client.py:134] - [2841925213904] 客户端会话已创建
2025-08-02 15:29:42 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:29:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:29:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF66390>
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:29:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:29:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEC5F0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:29:43 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEDD850>
2025-08-02 15:29:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:29:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:29:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:43 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:29:44 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b68e3ef7-7789-4f84-ad9a-bb4442b71505'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:29:44 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=b0b9daaa5e2a478690c176ab2bee0d24 "HTTP/1.1 202 Accepted"
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:29:44 - root - INFO - [client.py:138] - [2841925213904] SSE连接建立成功
2025-08-02 15:29:44 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:29:44 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6d107d01-f395-4dd3-a17a-a36f174f9ed4'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:29:44 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=b0b9daaa5e2a478690c176ab2bee0d24 "HTTP/1.1 202 Accepted"
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:29:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:29:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:29:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:29:45 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f1a6fb75-d738-40d7-97de-053e2f65986c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:29:45 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=b0b9daaa5e2a478690c176ab2bee0d24 "HTTP/1.1 202 Accepted"
2025-08-02 15:29:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:29:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:29:45 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:29:45 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:29:45 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:29:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:29:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:29:46 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:29:46 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:29:46 - root - DEBUG - [client.py:214] - [2841925213904] 开始清理SSE连接资源...
2025-08-02 15:29:46 - root - DEBUG - [client.py:219] - [2841925213904] 会话上下文已清理
2025-08-02 15:29:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:29:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afe49390')
2025-08-02 15:29:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:29:46 - root - DEBUG - [client.py:228] - [2841925213904] 上下文已清理
2025-08-02 15:29:46 - root - INFO - [client.py:234] - [2841925213904] SSE连接资源清理完成
2025-08-02 15:37:07 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:37:07 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:37:07 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:37:07 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:37:07 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:37:07 - root - INFO - [client.py:117] - [2841925197008] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:37:07 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:37:07 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:37:07 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF6790>
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:37:07 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:37:07 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77530> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:37:08 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF5050>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:37:08 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820117541202284773643e38c3f3d0e0e0fe524a88d6a715c70720093d;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820117541202284773643e38c3f3d0e0e0fe524a88d6a715c70720093d;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'c0e19638-5163-4c24-8c36-236f3785d1ac'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:37:08 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:37:08 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:37:08 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:37:08 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=9cf517fcd7f34147b2accc33165a35ba
2025-08-02 15:37:08 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=9cf517fcd7f34147b2accc33165a35ba
2025-08-02 15:37:08 - root - DEBUG - [client.py:129] - [2841925197008] SSE流连接已建立
2025-08-02 15:37:08 - root - DEBUG - [client.py:134] - [2841925197008] 客户端会话已创建
2025-08-02 15:37:08 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:37:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:37:08 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFECBA90>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:37:08 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:37:08 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77530> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:37:10 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF6B110>
2025-08-02 15:37:10 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:37:10 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:10 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:37:10 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:10 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:37:11 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9789abb7-f75a-4ec0-9b83-720ee18b54d7'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:37:11 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=9cf517fcd7f34147b2accc33165a35ba "HTTP/1.1 202 Accepted"
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:37:11 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:37:11 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:37:11 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:37:11 - root - INFO - [client.py:138] - [2841925197008] SSE连接建立成功
2025-08-02 15:37:11 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:37:11 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:11 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:37:12 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'ee577c26-d888-41a0-a88f-025448f5fd41'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:37:12 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=9cf517fcd7f34147b2accc33165a35ba "HTTP/1.1 202 Accepted"
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:37:12 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:37:12 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:37:12 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:37:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:37:13 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9311e3b8-67b9-40d6-b453-b3804346ae34'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:37:13 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=9cf517fcd7f34147b2accc33165a35ba "HTTP/1.1 202 Accepted"
2025-08-02 15:37:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:37:13 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:37:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:37:13 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:37:13 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:37:14 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:37:14 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:37:14 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:37:14 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:37:14 - root - DEBUG - [client.py:214] - [2841925197008] 开始清理SSE连接资源...
2025-08-02 15:37:14 - root - DEBUG - [client.py:219] - [2841925197008] 会话上下文已清理
2025-08-02 15:37:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:37:14 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff12e90')
2025-08-02 15:37:14 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:37:14 - root - DEBUG - [client.py:228] - [2841925197008] 上下文已清理
2025-08-02 15:37:14 - root - INFO - [client.py:234] - [2841925197008] SSE连接资源清理完成
2025-08-02 15:39:48 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:39:48 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:39:48 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:39:48 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:39:48 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:39:48 - root - INFO - [client.py:117] - [2841924979600] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:39:48 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:39:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:39:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF12A10>
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:39:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:39:48 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77A40> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:39:49 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF64C90>
2025-08-02 15:39:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:39:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:39:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:39:50 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820317541203900168081ed04cd191d284624b4f1938bee091a2c8090b;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820317541203900168081ed04cd191d284624b4f1938bee091a2c8090b;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9b384422-97cb-4cfe-baaa-cdfc390e43e1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:39:50 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:39:50 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:39:50 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:39:50 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=86d87442644548a5aa3aed6cdefb80c3
2025-08-02 15:39:50 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=86d87442644548a5aa3aed6cdefb80c3
2025-08-02 15:39:50 - root - DEBUG - [client.py:129] - [2841924979600] SSE流连接已建立
2025-08-02 15:39:50 - root - DEBUG - [client.py:134] - [2841924979600] 客户端会话已创建
2025-08-02 15:39:50 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:39:50 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:39:50 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF39D0>
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:39:50 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:39:50 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77A40> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:39:51 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF67390>
2025-08-02 15:39:51 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:39:51 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:51 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:39:51 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:51 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:39:52 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f26ed44a-bf92-4a4f-a758-0b2c2a95b38e'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:39:52 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=86d87442644548a5aa3aed6cdefb80c3 "HTTP/1.1 202 Accepted"
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:39:52 - root - INFO - [client.py:138] - [2841924979600] SSE连接建立成功
2025-08-02 15:39:52 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:39:52 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7b1e8dd1-f25c-45a4-8ddb-5a23efb4eade'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:39:52 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=86d87442644548a5aa3aed6cdefb80c3 "HTTP/1.1 202 Accepted"
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:39:52 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:39:52 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:39:53 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'98bb076b-614e-4d6c-ac36-4cb4fc4ad0c9'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:39:53 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=86d87442644548a5aa3aed6cdefb80c3 "HTTP/1.1 202 Accepted"
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:39:53 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:39:53 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:39:53 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:39:53 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:39:53 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:39:53 - root - DEBUG - [client.py:214] - [2841924979600] 开始清理SSE连接资源...
2025-08-02 15:39:53 - root - DEBUG - [client.py:219] - [2841924979600] 会话上下文已清理
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff14890')
2025-08-02 15:39:53 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:39:53 - root - DEBUG - [client.py:228] - [2841924979600] 上下文已清理
2025-08-02 15:39:53 - root - INFO - [client.py:234] - [2841924979600] SSE连接资源清理完成
2025-08-02 15:45:53 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 15:45:53 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 15:45:53 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 15:45:53 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 15:45:53 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 15:45:53 - root - INFO - [client.py:117] - [2841925157584] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:45:53 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 15:45:53 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 15:45:53 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF14D0>
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:45:53 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:45:53 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77B60> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 15:45:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF4790>
2025-08-02 15:45:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 15:45:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 15:45:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 07:45:55 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820217541207550285864e35da1e8264af2a0a6358daa19afb784df416;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820217541207550285864e35da1e8264af2a0a6358daa19afb784df416;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9df10109-5944-4489-b382-3d3e75cc1c9f'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:45:55 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 15:45:55 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 15:45:55 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 15:45:55 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=dfa0a5c119a84fa5a6968fa6cf8da93e
2025-08-02 15:45:55 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=dfa0a5c119a84fa5a6968fa6cf8da93e
2025-08-02 15:45:55 - root - DEBUG - [client.py:129] - [2841925157584] SSE流连接已建立
2025-08-02 15:45:55 - root - DEBUG - [client.py:134] - [2841925157584] 客户端会话已创建
2025-08-02 15:45:55 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 15:45:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 15:45:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF17F10>
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 15:45:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 15:45:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77B60> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 15:45:56 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF13750>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:45:56 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b9e187c4-a26b-43ac-a38a-7d35e3d68abc'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:45:56 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=dfa0a5c119a84fa5a6968fa6cf8da93e "HTTP/1.1 202 Accepted"
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:45:56 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:45:56 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:45:56 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 15:45:56 - root - INFO - [client.py:138] - [2841925157584] SSE连接建立成功
2025-08-02 15:45:56 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 15:45:56 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:45:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'c869127c-c63c-4a47-8166-e5e470bc7f0d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:45:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=dfa0a5c119a84fa5a6968fa6cf8da93e "HTTP/1.1 202 Accepted"
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:45:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:45:57 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 15:45:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 07:45:58 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'47e4f6d3-d386-4930-9ff1-b008ac3d7ebf'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 15:45:58 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=dfa0a5c119a84fa5a6968fa6cf8da93e "HTTP/1.1 202 Accepted"
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:45:58 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 15:45:58 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 15:45:58 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 15:45:58 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 15:45:58 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 15:45:58 - root - DEBUG - [client.py:214] - [2841925157584] 开始清理SSE连接资源...
2025-08-02 15:45:58 - root - DEBUG - [client.py:219] - [2841925157584] 会话上下文已清理
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff08790')
2025-08-02 15:45:58 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 15:45:58 - root - DEBUG - [client.py:228] - [2841925157584] 上下文已清理
2025-08-02 15:45:58 - root - INFO - [client.py:234] - [2841925157584] SSE连接资源清理完成
2025-08-02 15:50:35 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925159824] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925258960] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925217680] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925081616] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925167440] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFD9F8D0>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:33:42 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:33:42 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:33:42 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:33:42 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:33:42 - root - INFO - [client.py:117] - [2841925535376] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF7F50>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77020> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF0AF50>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF41710>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE4A110>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF1990>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76A80> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECA70> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECCB0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECE60> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:42 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:42 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEC830> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:33:43 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF08950>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:43 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B002FE50>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:43 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF7B10>
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF12C10>
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF093D0>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236239528188e004f1ec5808b8bd00a07c94c8bc87cf9a42a;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236239528188e004f1ec5808b8bd00a07c94c8bc87cf9a42a;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f5c9d7e5-a14f-4aad-bb1f-6bfd6805fe99'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:44 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=51b501b0e7d342febbd6a232403396e1
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=51b501b0e7d342febbd6a232403396e1
2025-08-02 16:33:44 - root - DEBUG - [client.py:129] - [2841925167440] SSE流连接已建立
2025-08-02 16:33:44 - root - DEBUG - [client.py:134] - [2841925167440] 客户端会话已创建
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236238208597e0052cb3e1756cfa960e0b12266970f19ad50;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236238208597e0052cb3e1756cfa960e0b12266970f19ad50;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd11961fe-f5ca-4be1-84ed-47ecc443cda3'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:44 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE4B010>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF41D0>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECE60> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=3c0622c3c2744b1ea168256d72582860
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=3c0622c3c2744b1ea168256d72582860
2025-08-02 16:33:44 - root - DEBUG - [client.py:129] - [2841925217680] SSE流连接已建立
2025-08-02 16:33:44 - root - DEBUG - [client.py:134] - [2841925217680] 客户端会话已创建
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFF3B50>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECA70> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236244697652e0067f5681698581320eebbe60a66080fadf8;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236244697652e0067f5681698581320eebbe60a66080fadf8;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'923b5877-68c7-4632-961a-d9818d262191'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:44 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236244974284e005e1cad40db4166983e3f958b8086896084;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236244974284e005e1cad40db4166983e3f958b8086896084;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'2430bb2e-f988-498e-a083-28856bd6b127'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:44 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236244816971e00583d7df97e0ab40c00a1c6ba997cedbb17;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236244816971e00583d7df97e0ab40c00a1c6ba997cedbb17;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'1a2951cb-e2b3-4802-9d6b-a70aebb9a1d8'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:44 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=1b404d8c5e8e41a780d0a38c6b125a1c
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=1b404d8c5e8e41a780d0a38c6b125a1c
2025-08-02 16:33:44 - root - DEBUG - [client.py:129] - [2841925081616] SSE流连接已建立
2025-08-02 16:33:44 - root - DEBUG - [client.py:134] - [2841925081616] 客户端会话已创建
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B002FDD0>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECCB0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=fc27ea3e1e8b4bdab6a1bb33751fd856
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=fc27ea3e1e8b4bdab6a1bb33751fd856
2025-08-02 16:33:44 - root - DEBUG - [client.py:129] - [2841925258960] SSE流连接已建立
2025-08-02 16:33:44 - root - DEBUG - [client.py:134] - [2841925258960] 客户端会话已创建
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=abff96d4db804fe7a85cc47702c7403a
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=abff96d4db804fe7a85cc47702c7403a
2025-08-02 16:33:44 - root - DEBUG - [client.py:129] - [2841925535376] SSE流连接已建立
2025-08-02 16:33:44 - root - DEBUG - [client.py:134] - [2841925535376] 客户端会话已创建
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:44 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFE7E90>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFD19D0>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF76A80> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:44 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:44 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEC830> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:33:44 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820917541236245216900e2d96de224c10cc836d15c9c759725c18fdf8;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820917541236245216900e2d96de224c10cc836d15c9c759725c18fdf8;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b157488b-d0bb-4dbe-9721-eac8c21e3478'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:45 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:33:45 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:33:45 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:33:45 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4c5e4a47b810430f956d25809e9161cf
2025-08-02 16:33:45 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4c5e4a47b810430f956d25809e9161cf
2025-08-02 16:33:45 - root - DEBUG - [client.py:129] - [2841925159824] SSE流连接已建立
2025-08-02 16:33:45 - root - DEBUG - [client.py:134] - [2841925159824] 客户端会话已创建
2025-08-02 16:33:45 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:33:45 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:33:45 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0049510>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:33:45 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFF77020> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:33:45 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFF3D90>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFE5390>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFF16D0>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:45 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFD10D0>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFD2390>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7ac09545-19dd-4e4e-b6cc-20792fa4811c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=51b501b0e7d342febbd6a232403396e1 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:46 - root - INFO - [client.py:138] - [2841925167440] SSE连接建立成功
2025-08-02 16:33:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'029fa214-74ac-4f3a-bb4a-10e9340d2da6'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=1b404d8c5e8e41a780d0a38c6b125a1c "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6b759848-ca21-41a6-9685-bec3f43c3de8'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=3c0622c3c2744b1ea168256d72582860 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:46 - root - INFO - [client.py:138] - [2841925217680] SSE连接建立成功
2025-08-02 16:33:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'59d04af0-3904-4a06-9ae5-7aa1bbefffdd'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=fc27ea3e1e8b4bdab6a1bb33751fd856 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:46 - root - INFO - [client.py:138] - [2841925081616] SSE连接建立成功
2025-08-02 16:33:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - root - INFO - [client.py:138] - [2841925258960] SSE连接建立成功
2025-08-02 16:33:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFE4ED0>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'18b79c15-6b03-4d49-807e-2b16362e3ef6'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=abff96d4db804fe7a85cc47702c7403a "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:46 - root - INFO - [client.py:138] - [2841925535376] SSE连接建立成功
2025-08-02 16:33:46 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:46 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4be794d7-c267-4e89-9938-3e58bdc21579'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=51b501b0e7d342febbd6a232403396e1 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'c886713b-2ebd-4662-a845-2d78cefe8d7b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=abff96d4db804fe7a85cc47702c7403a "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'71f92f7c-c9a5-4a67-8c73-b42378d97732'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:46 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=1b404d8c5e8e41a780d0a38c6b125a1c "HTTP/1.1 202 Accepted"
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:46 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:46 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4b84c9b1-13f7-42aa-b03d-1eb4294c3ca9'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=fc27ea3e1e8b4bdab6a1bb33751fd856 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'08185317-828e-4903-b49a-6e00cfb782f5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=3c0622c3c2744b1ea168256d72582860 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'8f9d70a2-5194-41ac-a39c-889398d47b41'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4c5e4a47b810430f956d25809e9161cf "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:33:47 - root - INFO - [client.py:138] - [2841925159824] SSE连接建立成功
2025-08-02 16:33:47 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'651d0157-dce6-4e92-abef-5a744fd312fb'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=51b501b0e7d342febbd6a232403396e1 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fb06fb38-dafb-466f-8b36-0d6449a3a9b4'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=fc27ea3e1e8b4bdab6a1bb33751fd856 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'a877ad2f-5ca2-4413-aa67-367bcc2c25f1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4c5e4a47b810430f956d25809e9161cf "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:47 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:47 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:47 - root - DEBUG - [client.py:214] - [2841925167440] 开始清理SSE连接资源...
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:33:47 - root - DEBUG - [client.py:219] - [2841925167440] 会话上下文已清理
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:47 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:47 - root - DEBUG - [client.py:214] - [2841925258960] 开始清理SSE连接资源...
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afef0f90')
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:33:47 - root - DEBUG - [client.py:219] - [2841925258960] 会话上下文已清理
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff217d0')
2025-08-02 16:33:47 - root - DEBUG - [client.py:228] - [2841925167440] 上下文已清理
2025-08-02 16:33:47 - root - INFO - [client.py:234] - [2841925167440] SSE连接资源清理完成
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - root - DEBUG - [client.py:228] - [2841925258960] 上下文已清理
2025-08-02 16:33:47 - root - INFO - [client.py:234] - [2841925258960] SSE连接资源清理完成
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:47 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:47 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:47 - root - DEBUG - [client.py:214] - [2841925081616] 开始清理SSE连接资源...
2025-08-02 16:33:47 - root - DEBUG - [client.py:219] - [2841925081616] 会话上下文已清理
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afef68d0')
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.failed exception=CancelledError('Cancelled by cancel scope 295afef68d0')
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - root - DEBUG - [client.py:228] - [2841925081616] 上下文已清理
2025-08-02 16:33:47 - root - INFO - [client.py:234] - [2841925081616] SSE连接资源清理完成
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:47 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'7e694a5b-2170-4797-b655-be8439d53e1c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:47 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=3c0622c3c2744b1ea168256d72582860 "HTTP/1.1 202 Accepted"
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:47 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:47 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:48 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:48 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:48 - root - DEBUG - [client.py:214] - [2841925217680] 开始清理SSE连接资源...
2025-08-02 16:33:48 - root - DEBUG - [client.py:219] - [2841925217680] 会话上下文已清理
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff14190')
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:48 - root - DEBUG - [client.py:228] - [2841925217680] 上下文已清理
2025-08-02 16:33:48 - root - INFO - [client.py:234] - [2841925217680] SSE连接资源清理完成
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:48 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'532dde6d-8fde-4e08-9e2d-a3e52bbe4fa5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:48 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=abff96d4db804fe7a85cc47702c7403a "HTTP/1.1 202 Accepted"
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:48 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:48 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:48 - root - DEBUG - [client.py:214] - [2841925535376] 开始清理SSE连接资源...
2025-08-02 16:33:48 - root - DEBUG - [client.py:219] - [2841925535376] 会话上下文已清理
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff655d0')
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:48 - root - DEBUG - [client.py:228] - [2841925535376] 上下文已清理
2025-08-02 16:33:48 - root - INFO - [client.py:234] - [2841925535376] SSE连接资源清理完成
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:33:48 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f712c3d6-9b6a-479d-a0cd-b5c5f0fad79a'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:33:48 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4c5e4a47b810430f956d25809e9161cf "HTTP/1.1 202 Accepted"
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:33:48 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:33:48 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:33:48 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:33:48 - root - DEBUG - [client.py:214] - [2841925159824] 开始清理SSE连接资源...
2025-08-02 16:33:48 - root - DEBUG - [client.py:219] - [2841925159824] 会话上下文已清理
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff0b810')
2025-08-02 16:33:48 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:33:48 - root - DEBUG - [client.py:228] - [2841925159824] 上下文已清理
2025-08-02 16:33:48 - root - INFO - [client.py:234] - [2841925159824] SSE连接资源清理完成
2025-08-02 16:34:28 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:34:28 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:34:28 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:34:28 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:34:28 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:34:28 - root - INFO - [client.py:117] - [2841925990608] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:34:28 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:34:28 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:34:28 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004B0D0>
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:34:28 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:34:28 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECF80> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:34:30 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004A410>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:34:30 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541236703907038e00593a85cb3efb0c71356559415744743775;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541236703907038e00593a85cb3efb0c71356559415744743775;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'8db666ac-4d19-4dca-b5a8-1eca5d26ea8b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:34:30 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:34:30 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:34:30 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:34:30 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a1bbeea02c4a440493e67be7923318dc
2025-08-02 16:34:30 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a1bbeea02c4a440493e67be7923318dc
2025-08-02 16:34:30 - root - DEBUG - [client.py:129] - [2841925990608] SSE流连接已建立
2025-08-02 16:34:30 - root - DEBUG - [client.py:134] - [2841925990608] 客户端会话已创建
2025-08-02 16:34:30 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:34:30 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:34:30 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B006B790>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:34:30 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:34:30 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEECF80> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:34:31 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B006BB90>
2025-08-02 16:34:31 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:34:31 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:31 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:34:31 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:31 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:34:32 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6e404df2-c65b-423c-9973-a8539fcd6dbf'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:34:32 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1bbeea02c4a440493e67be7923318dc "HTTP/1.1 202 Accepted"
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:34:32 - root - INFO - [client.py:138] - [2841925990608] SSE连接建立成功
2025-08-02 16:34:32 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:34:32 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'5fd5c286-8b3b-4c17-be0f-0ed8f26f731d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:34:32 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1bbeea02c4a440493e67be7923318dc "HTTP/1.1 202 Accepted"
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:34:32 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:34:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:34:33 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'c36ed696-0a2b-402e-8348-4dd724c5da67'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:34:33 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1bbeea02c4a440493e67be7923318dc "HTTP/1.1 202 Accepted"
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:34:33 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:34:33 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:34:33 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:34:33 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:34:33 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:34:33 - root - DEBUG - [client.py:214] - [2841925990608] 开始清理SSE连接资源...
2025-08-02 16:34:33 - root - DEBUG - [client.py:219] - [2841925990608] 会话上下文已清理
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afff3590')
2025-08-02 16:34:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:34:33 - root - DEBUG - [client.py:228] - [2841925990608] 上下文已清理
2025-08-02 16:34:33 - root - INFO - [client.py:234] - [2841925990608] SSE连接资源清理完成
2025-08-02 16:41:19 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:41:19 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:41:19 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:41:19 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:41:19 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:41:19 - root - INFO - [client.py:117] - [2841926111056] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:41:19 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:41:19 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:41:19 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0075590>
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:41:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:41:19 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEFC80> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:41:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0076110>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:41:20 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541240802834896e005d4ca836928c63c0cee8f386dfb81dfc8b;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541240802834896e005d4ca836928c63c0cee8f386dfb81dfc8b;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'5426223e-acae-4108-9cc2-eab7d9644971'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:41:20 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:41:20 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:41:20 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:41:20 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=0c302de76fcd4f5c8f1ccd7fda95f0e3
2025-08-02 16:41:20 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=0c302de76fcd4f5c8f1ccd7fda95f0e3
2025-08-02 16:41:20 - root - DEBUG - [client.py:129] - [2841926111056] SSE流连接已建立
2025-08-02 16:41:20 - root - DEBUG - [client.py:134] - [2841926111056] 客户端会话已创建
2025-08-02 16:41:20 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:41:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:41:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0074CD0>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:41:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:41:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEFC80> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:41:21 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE86E50>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:41:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'0a129369-3de4-4152-98dd-e75741dd65b7'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:41:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=0c302de76fcd4f5c8f1ccd7fda95f0e3 "HTTP/1.1 202 Accepted"
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:41:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:41:21 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:41:21 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:41:21 - root - INFO - [client.py:138] - [2841926111056] SSE连接建立成功
2025-08-02 16:41:21 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:41:21 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:41:22 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9d7a889e-b724-4ad1-90aa-ce2d8539c781'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:41:22 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=0c302de76fcd4f5c8f1ccd7fda95f0e3 "HTTP/1.1 202 Accepted"
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:41:22 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:41:22 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:41:22 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:41:22 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:41:22 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'25396c04-0bf9-4466-a65a-c1074ee427aa'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:41:22 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=0c302de76fcd4f5c8f1ccd7fda95f0e3 "HTTP/1.1 202 Accepted"
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:41:22 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:41:22 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:41:22 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:41:22 - root - DEBUG - [client.py:214] - [2841926111056] 开始清理SSE连接资源...
2025-08-02 16:41:22 - root - DEBUG - [client.py:219] - [2841926111056] 会话上下文已清理
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295b006a290')
2025-08-02 16:41:22 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:41:22 - root - DEBUG - [client.py:228] - [2841926111056] 上下文已清理
2025-08-02 16:41:22 - root - INFO - [client.py:234] - [2841926111056] SSE连接资源清理完成
2025-08-02 16:43:34 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:43:34 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:43:34 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:43:34 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:43:34 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:43:34 - root - INFO - [client.py:117] - [2841926654672] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:43:34 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:43:34 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:43:34 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0048290>
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:43:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:43:34 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEFF50> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:43:35 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004A710>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:43:35 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541242153645070e00512eb4bcd7bf3302e83b6512ba785d061a;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541242153645070e00512eb4bcd7bf3302e83b6512ba785d061a;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b4935986-9963-47ef-b2ea-9e1c997092a5'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:43:35 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:43:35 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:43:35 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:43:35 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4cc00914a0e144ada2ea759f80b8dad0
2025-08-02 16:43:35 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=4cc00914a0e144ada2ea759f80b8dad0
2025-08-02 16:43:35 - root - DEBUG - [client.py:129] - [2841926654672] SSE流连接已建立
2025-08-02 16:43:35 - root - DEBUG - [client.py:134] - [2841926654672] 客户端会话已创建
2025-08-02 16:43:35 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:43:35 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:43:35 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFECB5D0>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:43:35 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:43:35 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEFF50> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:43:36 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE86E50>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:43:36 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'732339a2-4645-4c9f-93c7-eced4b864f24'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:43:36 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4cc00914a0e144ada2ea759f80b8dad0 "HTTP/1.1 202 Accepted"
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:43:36 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:43:36 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:43:36 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:43:36 - root - INFO - [client.py:138] - [2841926654672] SSE连接建立成功
2025-08-02 16:43:36 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:43:36 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:36 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:43:37 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'caaf2eb4-5491-4a21-b30e-93ed12a5a11b'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:43:37 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4cc00914a0e144ada2ea759f80b8dad0 "HTTP/1.1 202 Accepted"
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:43:37 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:43:37 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:43:37 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'f99ae8ad-3e21-4605-a7eb-4fff7dca0398'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:43:37 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=4cc00914a0e144ada2ea759f80b8dad0 "HTTP/1.1 202 Accepted"
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:43:37 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:43:37 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:43:37 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:43:37 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:43:37 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:43:37 - root - DEBUG - [client.py:214] - [2841926654672] 开始清理SSE连接资源...
2025-08-02 16:43:37 - root - DEBUG - [client.py:219] - [2841926654672] 会话上下文已清理
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afef6c10')
2025-08-02 16:43:37 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:43:37 - root - DEBUG - [client.py:228] - [2841926654672] 上下文已清理
2025-08-02 16:43:37 - root - INFO - [client.py:234] - [2841926654672] SSE连接资源清理完成
2025-08-02 16:46:54 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:46:54 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:46:54 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:46:54 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:46:54 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:46:54 - root - INFO - [client.py:117] - [2841925083216] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:46:54 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:46:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:46:54 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B00690D0>
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:46:54 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:46:54 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEE720> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:46:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004F6D0>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:46:55 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820317541244153996716ed0629e2035ed5daceb6da76455bf0d03e806;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820317541244153996716ed0629e2035ed5daceb6da76455bf0d03e806;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4454c428-dc7d-4660-817f-d4c321ce3c20'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:46:55 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:46:55 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:46:55 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:46:55 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=d2c84cff227b4ab8b63262f7fb82b7e5
2025-08-02 16:46:55 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=d2c84cff227b4ab8b63262f7fb82b7e5
2025-08-02 16:46:55 - root - DEBUG - [client.py:129] - [2841925083216] SSE流连接已建立
2025-08-02 16:46:55 - root - DEBUG - [client.py:134] - [2841925083216] 客户端会话已创建
2025-08-02 16:46:55 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:46:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:46:55 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004E550>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:46:55 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:46:55 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEE720> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:46:56 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF082D0>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:46:56 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'02f6cd2b-fb74-44b1-ae66-bd4a236ccdf1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:46:56 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=d2c84cff227b4ab8b63262f7fb82b7e5 "HTTP/1.1 202 Accepted"
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:46:56 - root - INFO - [client.py:138] - [2841925083216] SSE连接建立成功
2025-08-02 16:46:56 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:46:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'043c6d5b-247d-41b2-a348-857937155d87'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:46:56 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=d2c84cff227b4ab8b63262f7fb82b7e5 "HTTP/1.1 202 Accepted"
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:46:56 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:46:56 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:46:57 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'6fb225dd-a2fe-46f1-bc62-0949fb858201'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:46:57 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=d2c84cff227b4ab8b63262f7fb82b7e5 "HTTP/1.1 202 Accepted"
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:46:57 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:46:57 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:46:57 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:46:57 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:46:57 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:46:57 - root - DEBUG - [client.py:214] - [2841925083216] 开始清理SSE连接资源...
2025-08-02 16:46:57 - root - DEBUG - [client.py:219] - [2841925083216] 会话上下文已清理
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295afef5210')
2025-08-02 16:46:57 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:46:57 - root - DEBUG - [client.py:228] - [2841925083216] 上下文已清理
2025-08-02 16:46:57 - root - INFO - [client.py:234] - [2841925083216] SSE连接资源清理完成
2025-08-02 16:53:23 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 16:53:23 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 16:53:23 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 16:53:23 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 16:53:23 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 16:53:23 - root - INFO - [client.py:117] - [2841925189968] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:53:23 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 16:53:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 16:53:23 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AEC4F790>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:53:23 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEEDE0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 16:53:23 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFED9A50>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:23 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 08:53:24 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=2760820317541248039357983ed05ca4e88dac3202a9ab8dd0ddffb6f2c3f6;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=2760820317541248039357983ed05ca4e88dac3202a9ab8dd0ddffb6f2c3f6;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'dfa6e5da-530e-44dc-a7e6-b413239774a2'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:53:24 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a1ee613e479d409c92e26caa4c30cb12
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a1ee613e479d409c92e26caa4c30cb12
2025-08-02 16:53:24 - root - DEBUG - [client.py:129] - [2841925189968] SSE流连接已建立
2025-08-02 16:53:24 - root - DEBUG - [client.py:134] - [2841925189968] 客户端会话已创建
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 16:53:24 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 16:53:24 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFEE990>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 16:53:24 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEEDE0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 16:53:24 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFEF910>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:53:24 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'd33369eb-3e7d-4bb4-a749-564d2e6f3826'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:53:24 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1ee613e479d409c92e26caa4c30cb12 "HTTP/1.1 202 Accepted"
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 16:53:24 - root - INFO - [client.py:138] - [2841925189968] SSE连接建立成功
2025-08-02 16:53:24 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 16:53:24 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:24 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:53:25 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'2a6633d4-dd01-43e3-bd36-1a60901ccdac'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:53:25 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1ee613e479d409c92e26caa4c30cb12 "HTTP/1.1 202 Accepted"
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:53:25 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:53:25 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 08:53:25 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'1bded96f-27f1-4a57-950d-6e788ec5469c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 16:53:25 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a1ee613e479d409c92e26caa4c30cb12 "HTTP/1.1 202 Accepted"
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:53:25 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 16:53:25 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 16:53:25 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 16:53:25 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 16:53:25 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 16:53:25 - root - DEBUG - [client.py:214] - [2841925189968] 开始清理SSE连接资源...
2025-08-02 16:53:25 - root - DEBUG - [client.py:219] - [2841925189968] 会话上下文已清理
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295aff12c90')
2025-08-02 16:53:25 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 16:53:25 - root - DEBUG - [client.py:228] - [2841925189968] 上下文已清理
2025-08-02 16:53:25 - root - INFO - [client.py:234] - [2841925189968] SSE连接资源清理完成
2025-08-02 17:28:32 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 17:28:32 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 17:28:32 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 17:28:32 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 17:28:32 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 17:28:32 - root - INFO - [client.py:117] - [2841923677328] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:28:32 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 17:28:32 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFEF4590>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:28:32 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEDB50> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 17:28:32 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE70AD0>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 09:28:33 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541269129342416e005cf1ddebc2c958828cb7c811161ce11ad1;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541269129342416e005cf1ddebc2c958828cb7c811161ce11ad1;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'bcdc866d-9cc2-4dc9-88a7-d33baefd5b57'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:28:32 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=491f79af42d14b9c95e0907fa60a9ca5
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=491f79af42d14b9c95e0907fa60a9ca5
2025-08-02 17:28:32 - root - DEBUG - [client.py:129] - [2841923677328] SSE流连接已建立
2025-08-02 17:28:32 - root - DEBUG - [client.py:134] - [2841923677328] 客户端会话已创建
2025-08-02 17:28:32 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 17:28:32 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 17:28:32 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE4A990>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:28:32 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:28:32 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEDB50> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 17:28:33 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0075210>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:28:33 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:28:33 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:28:33 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'b67596c9-cde1-4803-afb0-187c9f970835'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:28:33 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=491f79af42d14b9c95e0907fa60a9ca5 "HTTP/1.1 202 Accepted"
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:28:33 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:28:33 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 17:28:33 - root - INFO - [client.py:138] - [2841923677328] SSE连接建立成功
2025-08-02 17:28:33 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:28:33 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:28:34 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'0dc4ea49-9877-4170-bcb0-d907a18d4ed7'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:28:33 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=491f79af42d14b9c95e0907fa60a9ca5 "HTTP/1.1 202 Accepted"
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:28:34 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:28:34 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:28:34 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'02708b6a-6530-42ef-b12b-bfe47379c370'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:28:34 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=491f79af42d14b9c95e0907fa60a9ca5 "HTTP/1.1 202 Accepted"
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:28:34 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:28:34 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:28:34 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 17:28:34 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 17:28:34 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 17:28:34 - root - DEBUG - [client.py:214] - [2841923677328] 开始清理SSE连接资源...
2025-08-02 17:28:34 - root - DEBUG - [client.py:219] - [2841923677328] 会话上下文已清理
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295b0049dd0')
2025-08-02 17:28:34 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:28:34 - root - DEBUG - [client.py:228] - [2841923677328] 上下文已清理
2025-08-02 17:28:34 - root - INFO - [client.py:234] - [2841923677328] SSE连接资源清理完成
2025-08-02 17:31:47 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 17:31:47 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 17:31:47 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 17:31:47 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 17:31:47 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 17:31:47 - root - INFO - [client.py:117] - [2841925194768] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:31:47 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:31:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 17:31:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFEF810>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:31:48 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEE3C0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 17:31:48 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFFEEF10>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 09:31:48 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541271084777252e00669323180b42b45065d7296658ad232b24;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541271084777252e00669323180b42b45065d7296658ad232b24;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4074c715-ffcf-4d7d-8a91-adb83f8dc710'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:31:48 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 17:31:48 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 17:31:48 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 17:31:48 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=ea13de08aebc4579abca1f900326e3e9
2025-08-02 17:31:48 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=ea13de08aebc4579abca1f900326e3e9
2025-08-02 17:31:48 - root - DEBUG - [client.py:129] - [2841925194768] SSE流连接已建立
2025-08-02 17:31:48 - root - DEBUG - [client.py:134] - [2841925194768] 客户端会话已创建
2025-08-02 17:31:48 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 17:31:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 17:31:48 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFF43210>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:31:48 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEE3C0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 17:31:48 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AFE59110>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:48 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:31:49 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'2c4caa13-3439-4c05-8553-3234182a4c9c'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:31:49 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=ea13de08aebc4579abca1f900326e3e9 "HTTP/1.1 202 Accepted"
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 17:31:49 - root - INFO - [client.py:138] - [2841925194768] SSE连接建立成功
2025-08-02 17:31:49 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:31:49 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'823d1e99-b91c-486b-8081-36d3552c039d'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:31:49 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=ea13de08aebc4579abca1f900326e3e9 "HTTP/1.1 202 Accepted"
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:31:49 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'bf2d5e14-ef42-455d-84b3-2ea558e2c3b1'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:31:49 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=ea13de08aebc4579abca1f900326e3e9 "HTTP/1.1 202 Accepted"
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:31:49 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 17:31:49 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 17:31:49 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 17:31:49 - root - DEBUG - [client.py:214] - [2841925194768] 开始清理SSE连接资源...
2025-08-02 17:31:49 - root - DEBUG - [client.py:219] - [2841925194768] 会话上下文已清理
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295b0077390')
2025-08-02 17:31:49 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:31:49 - root - DEBUG - [client.py:228] - [2841925194768] 上下文已清理
2025-08-02 17:31:49 - root - INFO - [client.py:234] - [2841925194768] SSE连接资源清理完成
2025-08-02 17:39:19 - root - INFO - [server.py:68] - 方法:/v1/tool/list, https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, request headers: Headers({'content-type': 'application/json; charset=utf-8', 'content-length': '76', 'host': '127.0.0.1:8188', 'connection': 'Keep-Alive', 'accept-encoding': 'gzip', 'user-agent': 'okhttp/4.9.3'})
2025-08-02 17:39:19 - root - DEBUG - [client.py:80] - 设置连接超时时间: 5s
2025-08-02 17:39:19 - root - DEBUG - [client.py:84] - 设置SSE读取超时时间: 300s
2025-08-02 17:39:19 - root - DEBUG - [client.py:93] - 已更新自定义头部，共 0 个
2025-08-02 17:39:19 - root - DEBUG - [client.py:45] - SSE客户端初始化完成 - 服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse, 超时: 5s
2025-08-02 17:39:19 - root - INFO - [client.py:117] - [2841925162320] 正在连接到SSE服务器: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:39:19 - mcp.client.sse - DEBUG - [sse.py:56] - Connecting to SSE endpoint: https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse
2025-08-02 17:39:19 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=5 socket_options=None
2025-08-02 17:39:19 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295AEC4F790>
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:39:19 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:39:19 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEF0B0> server_hostname='mcp.api-inference.modelscope.net' timeout=5
2025-08-02 17:39:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004FF10>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'GET']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'GET']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 09:39:20 GMT'), (b'Content-Type', b'text/event-stream; charset=utf-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Set-Cookie', b'acw_tc=ac11000117541275603914141e00533b49a4b302cfdb27615b16694e5af6dc;path=/;HttpOnly;Max-Age=1800'), (b'Set-Cookie', b'acw_tc=ac11000117541275603914141e00533b49a4b302cfdb27615b16694e5af6dc;path=/;HttpOnly;Max-Age=1800'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Cache-Control', b'no-store'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'df665fe5-9f17-41eb-8047-bbca4323e8de'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:39:20 - httpx - INFO - [_client.py:1740] - HTTP Request: GET https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse "HTTP/1.1 200 OK"
2025-08-02 17:39:20 - mcp.client.sse - DEBUG - [sse.py:65] - SSE connection established
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'GET']>
2025-08-02 17:39:20 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: endpoint
2025-08-02 17:39:20 - mcp.client.sse - DEBUG - [sse.py:76] - Received endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a8791364595b4a5e8ad1a15162605ad9
2025-08-02 17:39:20 - mcp.client.sse - DEBUG - [sse.py:134] - Starting post writer with endpoint URL: https://mcp.api-inference.modelscope.net/messages/?session_id=a8791364595b4a5e8ad1a15162605ad9
2025-08-02 17:39:20 - root - DEBUG - [client.py:129] - [2841925162320] SSE流连接已建立
2025-08-02 17:39:20 - root - DEBUG - [client.py:134] - [2841925162320] 客户端会话已创建
2025-08-02 17:39:20 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='initialize', params={'protocolVersion': '2025-03-26', 'capabilities': {}, 'clientInfo': {'name': 'mcp', 'version': '0.1.0'}}, jsonrpc='2.0', id=0)), metadata=None)
2025-08-02 17:39:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.started host='127.0.0.1' port=7897 local_address=None timeout=30.0 socket_options=None
2025-08-02 17:39:20 - httpcore.connection - DEBUG - [_trace.py:87] - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B004F550>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'CONNECT']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'CONNECT']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'Connection established', [])
2025-08-02 17:39:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000295AFEEF0B0> server_hostname='mcp.api-inference.modelscope.net' timeout=30.0
2025-08-02 17:39:20 - httpcore.proxy - DEBUG - [_trace.py:87] - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000295B0069B10>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:20 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:39:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'fdabb979-9642-4794-ae46-7474a22aeea7'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:39:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a8791364595b4a5e8ad1a15162605ad9 "HTTP/1.1 202 Accepted"
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=0, result={'protocolVersion': '2024-11-05', 'capabilities': {'experimental': {}, 'resources': {'subscribe': False, 'listChanged': False}, 'tools': {'listChanged': False}}, 'serverInfo': {'name': '12306-mcp', 'version': '1.6.0'}})
2025-08-02 17:39:21 - root - INFO - [client.py:138] - [2841925162320] SSE连接建立成功
2025-08-02 17:39:21 - root - INFO - [client.py:278] - https://mcp.api-inference.modelscope.net/1784ac5c6d0044/sse 正在获取工具列表...
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCNotification(method='notifications/initialized', params=None, jsonrpc='2.0')), metadata=None)
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:39:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'9b479f40-4d6f-4c50-a55e-c8ab92b86018'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:39:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a8791364595b4a5e8ad1a15162605ad9 "HTTP/1.1 202 Accepted"
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:117] - Sending client message: SessionMessage(message=JSONRPCMessage(root=JSONRPCRequest(method='tools/list', params=None, jsonrpc='2.0', id=1)), metadata=None)
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_headers.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - send_request_body.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_headers.complete return_value=(b'HTTP/1.1', 202, b'Accepted', [(b'Date', b'Sat, 02 Aug 2025 09:39:21 GMT'), (b'Content-Type', b'application/octet-stream'), (b'Content-Length', b'8'), (b'Connection', b'keep-alive'), (b'Access-Control-Expose-Headers', b'Date,x-fc-request-id'), (b'Content-Disposition', b'attachment'), (b'X-Fc-Request-Id', b'4edfa338-3a27-4be5-846d-061c76c93ab2'), (b'Strict-Transport-Security', b'max-age=15724800; includeSubDomains')])
2025-08-02 17:39:21 - httpx - INFO - [_client.py:1740] - HTTP Request: POST https://mcp.api-inference.modelscope.net/messages/?session_id=a8791364595b4a5e8ad1a15162605ad9 "HTTP/1.1 202 Accepted"
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.started request=<Request [b'POST']>
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.complete
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:127] - Client message sent successfully: 202
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:72] - Received SSE event: message
2025-08-02 17:39:21 - mcp.client.sse - DEBUG - [sse.py:97] - Received server message: root=JSONRPCResponse(jsonrpc='2.0', id=1, result={'tools': [{'name': 'get-current-date', 'description': '获取当前日期，以上海时区（Asia/Shanghai, UTC+8）为准，返回格式为 "yyyy-MM-dd"。主要用于解析用户提到的相对日期（如“明天”、“下周三”），为其他需要日期的接口提供准确的日期输入。', 'inputSchema': {'type': 'object', 'properties': {}, 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-stations-code-in-city', 'description': '通过中文城市名查询该城市 **所有** 火车站的名称及其对应的 `station_code`，结果是一个包含多个车站信息的列表。', 'inputSchema': {'type': 'object', 'properties': {'city': {'type': 'string', 'description': '中文城市名称，例如："北京", "上海"'}}, 'required': ['city'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-of-citys', 'description': '通过中文城市名查询代表该城市的 `station_code`。此接口主要用于在用户提供**城市名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'citys': {'type': 'string', 'description': '要查询的城市，比如"北京"。若要查询多个城市，请用|分割，比如"北京|上海"。'}}, 'required': ['citys'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-code-by-names', 'description': '通过具体的中文车站名查询其 `station_code` 和车站名。此接口主要用于在用户提供**具体车站名**作为出发地或到达地时，为接口准备 `station_code` 参数。', 'inputSchema': {'type': 'object', 'properties': {'stationNames': {'type': 'string', 'description': '具体的中文车站名称，例如："北京南", "上海虹桥"。若要查询多个站点，请用|分割，比如"北京南|上海虹桥"。'}}, 'required': ['stationNames'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-station-by-telecode', 'description': '通过车站的 `station_telecode` 查询车站的详细信息，包括名称、拼音、所属城市等。此接口主要用于在已知 `telecode` 的情况下获取更完整的车站数据，或用于特殊查询及调试目的。一般用户对话流程中较少直接触发。', 'inputSchema': {'type': 'object', 'properties': {'stationTelecode': {'type': 'string', 'description': '车站的 `station_telecode` (3位字母编码)'}}, 'required': ['stationTelecode'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-tickets', 'description': '查询12306余票信息。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '到达地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空，即不筛选。支持多个标志同时筛选。例如用户说“高铁票”，则应使用 "G"。可选标志：[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 0, 'default': 0, 'description': '返回的余票数量限制，默认为0，即不限制。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-interline-tickets', 'description': '查询12306中转余票信息。尚且只支持查询前十条。', 'inputSchema': {'type': 'object', 'properties': {'date': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '查询日期，格式为 "yyyy-MM-dd"。如果用户提供的是相对日期（如“明天”），请务必先调用 `get-current-date` 接口获取当前日期，并计算出目标日期。'}, 'fromStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'toStation': {'type': 'string', 'description': '出发地的 `station_code` 。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'middleStation': {'type': 'string', 'default': '', 'description': '中转地的 `station_code` ，可选。必须是通过 `get-station-code-by-names` 或 `get-station-code-of-citys` 接口查询得到的编码，严禁直接使用中文地名。'}, 'showWZ': {'type': 'boolean', 'default': False, 'description': '是否显示无座车，默认不显示无座车。'}, 'trainFilterFlags': {'type': 'string', 'pattern': '^[GDZTKOFS]*$', 'maxLength': 8, 'default': '', 'description': '车次筛选条件，默认为空。从以下标志中选取多个条件组合[G(高铁/城际),D(动车),Z(直达特快),T(特快),K(快速),O(其他),F(复兴号),S(智能动车组)]'}, 'sortFlag': {'type': 'string', 'default': '', 'description': '排序方式，默认为空，即不排序。仅支持单一标识。可选标志：[startTime(出发时间从早到晚), arriveTime(抵达时间从早到晚), duration(历时从短到长)]'}, 'sortReverse': {'type': 'boolean', 'default': False, 'description': '是否逆向排序结果，默认为false。仅在设置了sortFlag时生效。'}, 'limitedNum': {'type': 'number', 'minimum': 1, 'default': 10, 'description': '返回的中转余票数量限制，默认为10。'}}, 'required': ['date', 'fromStation', 'toStation'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}, {'name': 'get-train-route-stations', 'description': '查询特定列车车次在指定区间内的途径车站、到站时间、出发时间及停留时间等详细经停信息。当用户询问某趟具体列车的经停站时使用此接口。', 'inputSchema': {'type': 'object', 'properties': {'trainNo': {'type': 'string', 'description': '要查询的实际车次编号 `train_no`，例如 "240000G10336"，而非"G1033"。此编号通常可以从 `get-tickets` 的查询结果中获取，或者由用户直接提供。'}, 'fromStationTelecode': {'type': 'string', 'description': '该列车行程的**出发站**的 `station_telecode` (3位字母编码`)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'toStationTelecode': {'type': 'string', 'description': '该列车行程的**到达站**的 `station_telecode` (3位字母编码)。通常来自 `get-tickets` 结果中的 `telecode` 字段，或者通过 `get-station-code-by-names` 得到。'}, 'departDate': {'type': 'string', 'minLength': 10, 'maxLength': 10, 'description': '列车从 `fromStationTelecode` 指定的车站出发的日期 (格式: yyyy-MM-dd)。如果用户提供的是相对日期，请务必先调用 `get-current-date` 解析。'}}, 'required': ['trainNo', 'fromStationTelecode', 'toStationTelecode', 'departDate'], 'additionalProperties': False, '$schema': 'http://json-schema.org/draft-07/schema#'}}]})
2025-08-02 17:39:21 - root - INFO - [client.py:283] - 成功获取 8 个工具
2025-08-02 17:39:21 - root - DEBUG - [client.py:288] - 工具列表: get-current-date, get-stations-code-in-city, get-station-code-of-citys, get-station-code-by-names, get-station-by-telecode, get-tickets, get-interline-tickets, get-train-route-stations
2025-08-02 17:39:21 - root - DEBUG - [client.py:214] - [2841925162320] 开始清理SSE连接资源...
2025-08-02 17:39:21 - root - DEBUG - [client.py:219] - [2841925162320] 会话上下文已清理
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.started
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - receive_response_body.failed exception=CancelledError('Cancelled by cancel scope 295b004d890')
2025-08-02 17:39:21 - httpcore.http11 - DEBUG - [_trace.py:87] - response_closed.complete
2025-08-02 17:39:21 - root - DEBUG - [client.py:228] - [2841925162320] 上下文已清理
2025-08-02 17:39:21 - root - INFO - [client.py:234] - [2841925162320] SSE连接资源清理完成
