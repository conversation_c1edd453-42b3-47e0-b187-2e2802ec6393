<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="cd08cd88-56ea-45a1-86a8-9238eb2c605a" name="Changes" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;customColor&quot;: &quot;&quot;,
  &quot;associatedIndex&quot;: 7
}</component>
  <component name="ProjectId" id="30iHUqgaH2pWC5TD3AAmc8Fpr2r" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "Application.GenieApplication.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true"
  }
}]]></component>
  <component name="RunManager">
    <configuration name="GenieApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.jd.genie.GenieApplication" />
      <module name="genie-backend" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.jd.genie.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.GenieApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="cd08cd88-56ea-45a1-86a8-9238eb2c605a" name="Changes" comment="" />
      <created>1754103492502</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1754103492502</updated>
    </task>
    <servers />
  </component>
</project>