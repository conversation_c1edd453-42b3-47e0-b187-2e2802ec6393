# -*- coding: utf-8 -*-
# =====================
#
#
# Author: liumin.423
# Date:   2025/7/8
# =====================
import json
import os
import aiohttp
from typing import List, Any, Optional

from genie_tool.util.log_util import timer, AsyncTimer
from genie_tool.util.sensitive_detection import SensitiveWordsReplace


async def call_zhipu_api_stream(
        messages: List[dict],
        model: str = "glm-4.5",
        temperature: float = 0.7,
        max_tokens: int = 8192,
        **kwargs
):
    """直接调用智谱AI API - 流式版本"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://open.bigmodel.cn/api/paas/v4")

    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")

    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json; charset=utf-8"
    }

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": True
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"智谱AI API调用失败: {response.status} - {error_text}")

            async for line in response.content:
                line = line.decode('utf-8').strip()
                if line.startswith('data: '):
                    data = line[6:]
                    if data == '[DONE]':
                        break
                    try:
                        chunk = json.loads(data)
                        if 'choices' in chunk and chunk['choices']:
                            delta = chunk['choices'][0].get('delta', {})
                            content = delta.get('content', '')
                            if content:
                                yield content
                    except json.JSONDecodeError:
                        continue


async def call_zhipu_api_non_stream(
        messages: List[dict],
        model: str = "glm-4.5",
        temperature: float = 0.7,
        max_tokens: int = 8192,
        **kwargs
):
    """直接调用智谱AI API - 非流式版本"""
    api_key = os.getenv("OPENAI_API_KEY")
    base_url = os.getenv("OPENAI_BASE_URL", "https://open.bigmodel.cn/api/paas/v4")

    if not api_key:
        raise ValueError("OPENAI_API_KEY environment variable is required")

    url = f"{base_url}/chat/completions"
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json; charset=utf-8"
    }

    payload = {
        "model": model,
        "messages": messages,
        "temperature": temperature,
        "max_tokens": max_tokens,
        "stream": False
    }

    async with aiohttp.ClientSession() as session:
        async with session.post(url, headers=headers, json=payload) as response:
            if response.status != 200:
                error_text = await response.text()
                raise Exception(f"智谱AI API调用失败: {response.status} - {error_text}")

            result = await response.json()
            if 'choices' in result and result['choices']:
                return result['choices'][0]['message']['content']
            else:
                raise Exception(f"智谱AI API返回格式错误: {result}")


@timer(key="enter")
async def ask_llm(
        messages: str | List[Any],
        model: str,
        temperature: float = None,
        top_p: float = None,
        stream: bool = False,

        # 自定义字段
        only_content: bool = False,     # 只返回内容

        extra_headers: Optional[dict] = None,
        **kwargs,
):
    if isinstance(messages, str):
        messages = [{"role": "user", "content": messages}]

    # 敏感词过滤
    if os.getenv("SENSITIVE_WORD_REPLACE", "false") == "true":
        for message in messages:
            if isinstance(message.get("content"), str):
                message["content"] = SensitiveWordsReplace.replace(message["content"])
            else:
                message["content"] = json.loads(
                    SensitiveWordsReplace.replace(json.dumps(message["content"], ensure_ascii=False)))

    # 使用直接调用智谱AI API的方式
    if model.startswith("glm"):
        async with AsyncTimer(key=f"exec ask_llm"):
            if stream:
                async for chunk in call_zhipu_api_stream(
                    messages=messages,
                    model=model,
                    temperature=temperature or 0.7,
                    **kwargs
                ):
                    yield chunk
            else:
                result = await call_zhipu_api_non_stream(
                    messages=messages,
                    model=model,
                    temperature=temperature or 0.7,
                    **kwargs
                )
                yield result
    else:
        # 对于其他模型，仍然使用litellm
        try:
            from litellm import acompletion
            response = await acompletion(
                messages=messages,
                model=model,
                temperature=temperature,
                top_p=top_p,
                stream=stream,
                extra_headers=extra_headers,
                **kwargs
            )
            async with AsyncTimer(key=f"exec ask_llm"):
                if stream:
                    async for chunk in response:
                        if only_content:
                            if chunk.choices and chunk.choices[0] and chunk.choices[0].delta and chunk.choices[0].delta.content:
                                yield chunk.choices[0].delta.content
                        else:
                            yield chunk
                else:
                    yield response.choices[0].message.content if only_content else response
        except ImportError:
            raise Exception("litellm not available for non-GLM models")


if __name__ == "__main__":
    pass
